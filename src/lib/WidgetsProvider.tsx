import React, { createContext, useContext, useState } from 'react';
import type { Widget } from './types';

interface WidgetsContextType {
  widgets: Widget[];
  addWidget: (widget: Widget) => void;
  removeWidget: (widgetId: string) => void;
}

interface WidgetsProviderProps {
  children: React.ReactNode;
  initialWidgets?: Widget[];
}

const WidgetsContext = createContext<WidgetsContextType | undefined>(undefined);

export const useWidgets = (): WidgetsContextType => {
  const context = useContext(WidgetsContext);
  if (!context) {
    throw new Error('useWidgets must be used within a WidgetsProvider');
  }
  return context;
};

export const WidgetsProvider: React.FC<WidgetsProviderProps> = ({
  children,
  initialWidgets = [],
}) => {
  const [widgets, setWidgets] = useState<Widget[]>(initialWidgets);

  const addWidget = (widget: Widget) => {
    setWidgets(prev => {
      if (prev.some(w => w.id === widget.id)) {
        console.warn(`Widget with id "${widget.id}" already exists`);
        return prev;
      }
      return [...prev, widget];
    });
  };

  const removeWidget = (widgetId: string) => {
    setWidgets(prev => prev.filter(w => w.id !== widgetId));
  };

  const contextValue: WidgetsContextType = {
    widgets,
    addWidget,
    removeWidget,
  };

  return (
    <WidgetsContext.Provider value={contextValue}>
      {children}
    </WidgetsContext.Provider>
  );
};

export default WidgetsProvider;
